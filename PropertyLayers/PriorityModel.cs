using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace PropertyLayers
{

    [DataContract]
    public class ResponseAPI
    {
        [DataMember]
        public bool status { get; set; }

        [DataMember]
        public string message { get; set; }
    }

    [DataContract]
    [Serializable]
    [BsonIgnoreExtraElements]
    public class PriorityModel
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 LeadPoints { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<long> ActiveLeadSet { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadSource { get; set; }

        private DateTime leadDate;

        [DataMember(EmitDefaultValue = false)]
        public DateTime LeadCreatedOn
        {
            get
            {
                if (leadDate.Kind == DateTimeKind.Utc)
                {
                    leadDate = leadDate.ToLocalTime();
                }
                return leadDate;
            }
            set
            {
                this.leadDate = value;
            }
        }

        [DataMember(EmitDefaultValue = false)]
        public long CustID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 LeadRank { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public SupplierData Supplier { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public LeadStatusData LeadStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public userData User { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public RevisitData Revisit { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public CallBackData CallBack { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public CallData Call { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public EventTypeEnum EventType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public LeadCategoryEnum LeadCategory { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]// Reason for Lead Coming in Priority
        public string Reason { get; set; }

        private DateTime Skiptime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime SkippingTime
        {
            get
            {
                if (Skiptime.Kind == DateTimeKind.Utc)
                {
                    Skiptime = Skiptime.ToLocalTime();
                }
                return Skiptime;
            }
            set
            {
                this.Skiptime = value;
            }
        }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]// EmailRevert
        public bool IsEmailRevert { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]// Inbound flag
        public bool IsInbound { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]// selection flag
        public bool IsSelection { get; set; }

        [DataMember(EmitDefaultValue = false)]// Referral flag
        public bool IsReferral { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]// NRI flag
        public bool IsNRI { get; set; }

        private DateTime pointDeductionTime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime PointDeductTime
        {
            get
            {
                if (pointDeductionTime.Kind == DateTimeKind.Utc)
                {
                    pointDeductionTime = pointDeductionTime.ToLocalTime();
                }
                return pointDeductionTime;
            }
            set { pointDeductionTime = value; }
        }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]// Flag if The lst bucket was Rest
        public bool IsLastBucketRest { get; set; }

        private DateTime PolicyExpDate;
        [DataMember(EmitDefaultValue = false)]// Mainly for Motor
        public DateTime PrevPolicyExpDate
        {
            get
            {
                if (PolicyExpDate.Kind == DateTimeKind.Utc)
                {
                    PolicyExpDate = PolicyExpDate.ToLocalTime();
                }
                return PolicyExpDate;
            }
            set { PolicyExpDate = value; }
        }

        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public bool MongoOperation { get; set; }

        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public bool LeadPointOperation { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public byte CallReleaseCount { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 SkipDurationHrs { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 ReleaseStatus { get; set; }

        private DateTime expectedAppearTime;
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime ExpectedAppearingTime
        {
            get
            {
                if (expectedAppearTime.Kind == DateTimeKind.Utc)
                {
                    expectedAppearTime = expectedAppearTime.ToLocalTime();
                }
                return expectedAppearTime;
            }
            set { expectedAppearTime = value; }
        }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public byte star { get; set; }


        private DateTime callReleasets;
        [DataMember(EmitDefaultValue = false)]
        public DateTime CallReleaseTime
        {
            get
            {
                if (callReleasets.Kind == DateTimeKind.Utc)
                {
                    callReleasets = callReleasets.ToLocalTime();
                }
                return callReleasets;
            }
            set { callReleasets = value; }
        }


        private DateTime PaymentAttemptTs;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime PaymentAttemptTime
        {
            get
            {
                if (PaymentAttemptTs.Kind == DateTimeKind.Utc)
                {
                    PaymentAttemptTs = PaymentAttemptTs.ToLocalTime();
                }
                return PaymentAttemptTs;
            }
            set { PaymentAttemptTs = value; }
        }


        private DateTime _PaymentFailureTime;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime PaymentFailureTime
        {
            get
            {
                if (_PaymentFailureTime.Kind == DateTimeKind.Utc)
                {
                    _PaymentFailureTime = _PaymentFailureTime.ToLocalTime();
                }
                return _PaymentFailureTime;
            }
            set { _PaymentFailureTime = value; }
        }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string PageName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsActive { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 Country { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime DOB { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool EmailOnly { get; set; }

        private DateTime _TripStart;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime TripStart
        {
            get
            {
                if (_TripStart.Kind == DateTimeKind.Utc)
                {
                    _TripStart = _TripStart.ToLocalTime();
                }
                return _TripStart;
            }

            set { _TripStart = value; }
        }

        private DateTime _TripEnd;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime TripEnd
        {
            get
            {
                if (_TripEnd.Kind == DateTimeKind.Utc)
                {
                    _TripEnd = _TripEnd.ToLocalTime();
                }
                return _TripEnd;
            }

            set { _TripEnd = value; }
        }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 SumInsured { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 BookedId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool IsBooked { get; set; }

        private DateTime _BookedTime;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime BookedTime
        {
            get
            {
                if (_BookedTime.Kind == DateTimeKind.Utc)
                {
                    _BookedTime = _BookedTime.ToLocalTime();
                }
                return _BookedTime;
            }

            set { _BookedTime = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public TicketObj Ticket { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public Int16 Counter { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public string MobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]   // Customer level Unasnwered attempt in a day
        public Int16 CustomerNANCAttempt { get; set; }

        private DateTime _CustomerCalltime;
        [DataMember(EmitDefaultValue = false)]   // Customer level Unasnwered attempt in a day
        public DateTime CustomerCalltime
        {
            get
            {
                if (_CustomerCalltime.Kind == DateTimeKind.Utc)
                {
                    _CustomerCalltime = _CustomerCalltime.ToLocalTime();
                }
                return _CustomerCalltime;
            }

            set { _CustomerCalltime = value; }
        }

        [DataMember(EmitDefaultValue = false)]   // Customer level Unasnwered attempt in a day
        public Int16 CustomerTodayNANC { get; set; }

        private DateTime _ProposalError;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime ProposalError
        {
            get
            {
                if (_ProposalError.Kind == DateTimeKind.Utc)
                {
                    _ProposalError = _ProposalError.ToLocalTime();
                }
                return _ProposalError;
            }
            set { _ProposalError = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 NRIAreaCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int64 RevisitCount { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Decimal LeadPriorityScore { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public short InvestmentTypeId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public DateTime? GraceEndDate;
        [DataMember(EmitDefaultValue = false)]
        public DateTime? EmiDueDate;


        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime PFFilledTime
        {
            get
            {
                if (_PFFilledTime.Kind == DateTimeKind.Utc)
                {
                    _PFFilledTime = _PFFilledTime.ToLocalTime();
                }
                return _PFFilledTime;
            }
            set { _PFFilledTime = value; }
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime RMCSTime
        {
            get
            {
                if (_RMCSTime.Kind == DateTimeKind.Utc)
                {
                    _RMCSTime = _RMCSTime.ToLocalTime();
                }
                return _RMCSTime;
            }
            set { _RMCSTime = value; }
        }

        private DateTime _PFFilledTime;
        private DateTime _RMCSTime;

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 ScoreModel { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool IsAppointed { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DncData DNC { get; set; }

        private DateTime _AppointmentTime;

        [BsonElement("CFProc")]
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string CallFreqProcess;

        [DataMember(EmitDefaultValue = false)]
        public DateTime AppointmentTime
        {
            get
            {
                if (_AppointmentTime.Kind == DateTimeKind.Utc)
                {
                    _AppointmentTime = _AppointmentTime.ToLocalTime();
                }
                return _AppointmentTime;
            }
            set { _AppointmentTime = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public AppointmentData Appointment { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime RetentionOn { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 PinCode { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 RenewalYear { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 Income { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string CountryName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public CustEmergencyNo CustEmergencyNo { get; set; }

        [BsonElement("N_C")]
        [DataMember(EmitDefaultValue = false)]
        public string NriCity { get; set; }
        [BsonElement("U_S")]
        [DataMember(EmitDefaultValue = false)]
        public string Utm_source { get; set; }
    }

    [DataContract]
    public class SupplierData
    {
        [DataMember(EmitDefaultValue = false)]
        public int PlanID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 SupplierID { get; set; }
    }

    [DataContract]
    public class LeadStatusData
    {
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public List<long> Leads { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public byte StatusID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 SubStatusID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime Statustime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Status { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public string SubStatusName { get; set; }
    }

    [DataContract]
    [BsonIgnoreExtraElements]
    public class userData
    {
        [BsonIgnore]
        public Int32 AssignedID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }

        private DateTime _AssignedOn;
        [DataMember(EmitDefaultValue = false)]
        public DateTime AssignedOn
        {
            get
            {
                if (_AssignedOn.Kind == DateTimeKind.Utc)
                {
                    _AssignedOn = _AssignedOn.ToLocalTime();
                }
                return _AssignedOn;
            }
            set { _AssignedOn = value; }
        }

        private DateTime _FirstAssignedOn;
        [DataMember(EmitDefaultValue = false)]
        public DateTime FirstAssignedOn
        {
            get
            {
                if (_FirstAssignedOn == DateTime.MinValue)
                    _FirstAssignedOn = AssignedOn.ToLocalTime().Date;
                else if (_FirstAssignedOn.Kind == DateTimeKind.Utc)
                {
                    _FirstAssignedOn = _FirstAssignedOn.ToLocalTime();
                }

                return _FirstAssignedOn;
            }
            set { _FirstAssignedOn = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Grade { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 GroupId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool Reassigned { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public short JobID { get; set; }
    }

    [DataContract]
    public class RevisitData
    {
        private DateTime RevisitTime;

        [DataMember(EmitDefaultValue = false)]
        public DateTime ts
        {
            get
            {
                if (RevisitTime.Kind == DateTimeKind.Utc)
                {
                    RevisitTime = RevisitTime.ToLocalTime();
                }
                return RevisitTime;
            }
            set { this.RevisitTime = value; }
        }



        [DataMember(EmitDefaultValue = false)]
        public RevisitType RevisitType { get; set; }
    }

    public enum RevisitType
    {
        Default = 0,
        Inbound = 1,
        Email = 2,
        WebVisit = 3,
        Ctc = 4,
        BajajCustomer = 5,
        QuoteShared = 6,
        RevisionShared = 7,
        Chat = 8
    }

    [DataContract]
    public class CallBackData
    {
        private DateTime cbTime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime CBtime
        {
            get
            {
                if (cbTime.Kind == DateTimeKind.Utc)
                {
                    cbTime = cbTime.ToLocalTime();
                }
                return cbTime;
            }
            set { cbTime = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public byte Duration { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public CallBackTypeEnum CallBackType { get; set; }

        private DateTime CBCreatedTime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts
        {
            get
            {
                if (CBCreatedTime.Kind == DateTimeKind.Utc)
                {
                    CBCreatedTime = CBCreatedTime.ToLocalTime();
                }
                return CBCreatedTime;
            }
            set { this.CBCreatedTime = value; ; }
        }

        [DataMember(EmitDefaultValue = false)]
        public bool IsPaymentCB { get; set; }
    }

    public enum CallBackTypeEnum
    {
        Default = 0,
        CustRequested = 1,
        CustAgreed = 2,
        AgentBestGuess = 3,
        SystemSetCB = 4,
        PODTransfer = 5,
        BMSBookedCB = 6
    }

    [DataContract]
    public class TicketObj
    {
        [DataMember(EmitDefaultValue = false)]
        public String ReqBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime ts { get; set; }
    }

    [DataContract]
    [BsonIgnoreExtraElements]
    public class CallData
    {
        private DateTime callDate;
        [DataMember(EmitDefaultValue = false)]
        public DateTime calltime
        {
            get
            {
                if (callDate.Kind == DateTimeKind.Utc)
                {
                    callDate = callDate.ToLocalTime();
                }
                return callDate;
            }
            set { callDate = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public Int32 Duration { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 TalkTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CallType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Disposition { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 lastNCallTT { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 TotalTT { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 CallAttempts { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 NANC_Attempts { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<string> Shifts { get; set; }// This will contain  M A E for morning ,afternoon and Evening

        [DataMember(EmitDefaultValue = false)]   // flag if true Need to deduct point 
        public bool DeductPoint { get; set; }

        [DataMember(EmitDefaultValue = false)]   // flag if true Need to deduct point 
        public Int16 TodaysAttempt { get; set; }

        [DataMember(EmitDefaultValue = false)]   // NOT PICKING IN priority
        public Int16 TodaysNANCAttempt { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Week_Attempt { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Current_Week { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 uid { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 TodayAnswered { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Boolean IsProcessed { get; set; }
        [DataMember(EmitDefaultValue = false)]   // NOT PICKING IN priority
        public Int16 LastNminuteNANCeAttempts { get; set; } // Nminute=30 Minutes as of now
        [DataMember(EmitDefaultValue = false)]   // NOT PICKING IN priority
        public Int32 TodayTalktime { get; set; }
        [DataMember(EmitDefaultValue = false)]   // NOT PICKING IN priority
        [BsonIgnore]
        public DateTime LastCallTime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime LastConnectedCall { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int64 CallingNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 ReasonID { get; set; }
    }

    public enum EventTypeEnum
    {
        Default = 0,
        selection = 1,
        Assignment = 2,
        Call = 3,
        CallBack = 4,
        Revisit = 5,
        statusupdate = 6,
        Reject = 7,
        Book = 8,
        NewLead = 9,
        SingleNA1Hr = 10,
        ReleaseLeads = 11,
        SkipLead = 12,// Not in Use Right Now,
        OneTimeDataPreparation = 13
    }

    public enum LeadCategoryEnum
    {
        Default = 0,
        PaymentCB = 1,
        ActiveRevisit = 2,
        ActiveNew = 3,
        ActiveCB = 4,
        PassiveRevisit = 5,
        PassiveNew = 6,
        PassiveCB = 7,
        SecondAttemptPCB = 8,
        SecondAttemptActvRevisit = 9,
        SecondAttemptActvNew = 10,
        SecondAttemptActvCB = 11,
        SecondAttemptPasvRevisit = 12,
        SecondAttemptPasvNew = 13,
        SecondAttemptPasvCB = 14,
        UnansweredLeads = 15,
        RestLeads_1 = 16,
        SecondAttemptRestLeads = 17,
        RestLeads_2 = 18,
        CallReleasedLeads = 19,
        RecentExpiry = 20,
        SkippedLeads = 21,
        FutureCallBackLeads = 22,
        BdayLeads = 23,
        UnansweredRecentLeads = 24,
        EmailRevisit = 25,
        SecondAttemptEmailRevisit = 26,
        PaymentFailure = 27,
        SecondAttemptPaymentFailure = 28,
        CTCRevisit = 29,
        BookedLead = 30,
        RevisitAddLead = 31,
        TopAddLead = 32,
        allLeadpopup = 33,
        Search = 34,
        CTC = 35,
        SecondAttemptRevisitCTC = 36,
        PredictiveAdd = 37,
        BajajCustomerRevisit = 38,
        MissedCB = 39,
        TicketUpdate = 40,
        StatusChange = 41,
        BookedCB = 42,
        NoLeadPopup = 43,
        ProposalError = 44,
        QuoteShared = 45,
        RestPriorityLeads = 46,
        RevisionShared = 47,
        TodayExpiry = 48,
        SecondAttemptTodayExpiry = 49,
        PFFilled = 50,
        SecondAttemptPFFilled = 51,
        RMLeads = 52,
        CancelBookedLead = 53,
        SOSDocTickets = 54,
        SOSRest = 55,
        NewAssignemnt = 56,
        CTCLead = 57,
        ServiceCB = 58,
        VisitToday = 59,
        SecondAttemptVisitToday = 60,
        ConfirmVisit = 61,
        SecondAttemptConfirmVisit = 62,
        MissedVisit = 63,
        SecondAttemptMissedVisit = 64,
        VisitUpdate = 65,
        SecondAttemptVisitUpdate = 66,
        FOSChurn = 67,
        SecondAttemptFOSChurn = 68,
        CusatomerCB=69,
        ExpiryLogic = 70,
        TLCalling = 71,
        RenewalRest=72,
        SpouseOpportunity = 73,
        ExpiryLogicold = 74

    }

    [DataContract]
    public class DncData
    {
        private DateTime DncTime;
        private DateTime IbCallTime;
        [DataMember(EmitDefaultValue = false)]
        public Int16 CoolingPeriod { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal Score { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts
        {
            get
            {
                if (DncTime.Kind == DateTimeKind.Utc)
                {
                    DncTime = DncTime.ToLocalTime();
                }
                return DncTime;
            }
            set { this.DncTime = value; }
        }

        [BsonElement("ib_ts")]
        [DataMember(EmitDefaultValue = false)]
        public DateTime LastIbCallTime
        {
            get
            {
                if (IbCallTime.Kind == DateTimeKind.Utc)
                {
                    IbCallTime = IbCallTime.ToLocalTime();
                }
                return IbCallTime;
            }
            set { this.IbCallTime = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public CallSentimentEnum CallSentiment { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DNCModelEnum DncModel { get; set; }
    }

    [DataContract]
	[BsonIgnoreExtraElements]
    public class AppointmentData
    {
        [DataMember(EmitDefaultValue = false)]
        public DateTime ScheduledOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 StatusID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public AppointmentSourceEnum Source { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 AppointmentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime UpdatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 PCode { get; set; }
    }


    public enum AppointmentSourceEnum
    {
        Default = 0,
        Customer = 1
    }

    public class Next5WidgetLead
    {
        public long LeadId;
        public string Name;
        public DateTime ts;
        public string Reason;
        public byte Priority;
        public long CustomerId;
        public short ProductId;
        [BsonIgnoreIfDefault]
        public string CallStatus;
        public short ReasonId;
        public bool EmailOnly;
        public bool IsReligare;
        public Int16 Counter;
        public string ProposalNo;
        public long BookingId;
        public string EncryptedLeadId;
        public int IsAddLeadtoQueue;
        public int IsNeedToValidate;
        public Int64 CallDataID;
        public Int64 MobileNo;
        public DateTime CustConnectTime;
    }

    public class CountryTimeZone
    {
        public short CountryId { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public short AreaCode { get; set; }
        public string Country { get; set; }
    }

    public class NriCityTimeZone
    {
        public string NriCity { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string TimeZone { get; set; }
        public string TimeDiffFromIST { get; set; }
    }

    public class UserNext5Leads
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        public long UserId;

        public List<Next5WidgetLead> Leads;

        [BsonIgnoreIfDefault]
        public List<Next5WidgetLead> BookedLeads;
        [BsonIgnoreIfDefault]
        public bool IsAuto;

        [DataMember(EmitDefaultValue = false)]
        public string BMSUserToken { get; set; }
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }

    }

    [DataContract]
    public class OneLeadScoreMapping
    {
        public string Type { get; set; }
        public Int16 MinValue { get; set; }
        public Int16 MaxValue { get; set; }
        public Decimal Score { get; set; }
        public Int16 ProductID { get; set; }
    }

    public enum AppointmentEnumStatus
    {
        Booked = 2002,
        Completed = 2003,
        Cancelled = 2004,
        ReScheduled = 2005,
        Confirmed = 2088,
        Started = 2124
    }

    [DataContract]
    [Serializable]
    [BsonIgnoreExtraElements]
    public class SysConfigData
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]

        public string ID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string source { get; set; }


        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string clientKey { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string authKey { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string createdBy { get; set; }
    }

    [DataContract]
    [Serializable]
    public class ACLConfigData
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]

        public object ID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string source { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string method { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool isActive { get; set; }
    }

    public class User
    {
        public string UserId { get; set; }
        public string AsteriskToken { get; set; }
        public string EmployeeId { get; set; }
        public int GroupId { get; set; }
    }


    [DataContract]
    [Serializable]
    public class LeadPriorityLog
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime? LeadAppearedAt { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte? AppearedReasonId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16? Position { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte? IsPriorityLead { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime? WorkDoneTs { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte? ActionId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool FlagPositioningLog { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64? UserID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CallId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16? AddToQueue { get; set; }
    }

    public class LeadPointsDTO
    {
        public long LeadId;
        public short Points;
        public byte PointType;
        public byte PointSubType;
        public bool IsReset;
        public bool IsPaymentCB;
        public string Remarks;
        public bool IsPointDeducted;
        public short CurrentWeek;
        public short WeekPoints;
        public DateTime FirstAssignedOn;
    }

    public class OnlineCustomerInfo
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        public string Page { get; set; }
        public string CustName { get; set; }
        private DateTime _ts;
        public DateTime ts
        {
            get
            {
                if (_ts.Kind == DateTimeKind.Utc)
                {
                    _ts = _ts.ToLocalTime();
                }
                return _ts;
            }
            set { _ts = value; }
        }
        public Int64 AgentId { get; set; }
        public string RoomCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long visitLead { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long visitEnquiryID { get; set; }
    }

    [DataContract]
    public class QuoteSharedTracking
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 AgentId { get; set; }
    }
    [DataContract]
    public class UserDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Grade
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsActive
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<UserProductMapping> Products
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserName
        { get; set; }
    }
    [DataContract]
    public class GroupDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 GroupId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string GroupName
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsBMSGroup
        { get; set; }
    }

    [DataContract]
    public class UserProductMapping
    {
        [DataMember(EmitDefaultValue = false)]
        public List<GroupDetails> Groups
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ProductName
        { get; set; }
    }
    public enum Actions
    {
        Default = 0,
        LeadOpened = 1,
        CallInitiated = 2,
        CallEnded = 3,
        DoneClicked = 4,
        NoLeadPopup = 5
    }
    [DataContract]
    public class ReleaseLeadRequest
    {
        [DataMember(EmitDefaultValue = false)]
        public List<Int64> LeadIds { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Status { get; set; }
    }
    public class UnAnsweredSummary
    {
        [DataMember(EmitDefaultValue = true)]
        public Int32 WeekAttempts { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int32 CurrentWeek { get; set; }

        private DateTime _AttemptsTillDate;
        [DataMember(EmitDefaultValue = false)]
        public DateTime AttemptsTillDate
        {
            get
            {
                if (_AttemptsTillDate == DateTime.MinValue)
                {
                    _AttemptsTillDate = DateTime.Now;
                }
                else if (_AttemptsTillDate != null && _AttemptsTillDate.Kind == DateTimeKind.Utc)
                {
                    _AttemptsTillDate = _AttemptsTillDate.ToLocalTime();
                }
                return _AttemptsTillDate;
            }
            set { _AttemptsTillDate = value; }
        }

        [DataMember(EmitDefaultValue = true)]
        public Int32 TodayNANC { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int32 MaxAttempts { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int32 CustomerNANC { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int32 CustomerTodayNANC { get; set; }
    }
    public class ExpiryPriorityLogic
    {
        public Int16 MinExpDays { get; set; }
        public Int16 MaxExpDays { get; set; }
        public Int16 Attempts { get; set; }
        public Int16 CallingGapDays { get; set; }
        public Int16 GroupID { get; set; }
        public Int16 RYStart { get; set; }
        public Int16 RYEnd { get; set; }
        public Int16 Priority { get; set; }
        public bool IsNRI { get; set; }
        public Int32 MinTalkTime { get; set; }
        public Int32 MaxTalkTime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Process { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string FilterDate { get; set; }

    }
    [DataContract]
    public class NotContactedLeads
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime EventDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 CallbackType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long CustID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string StatusName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Country { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CallTiming { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int TotalTT { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int TotalAttempts { get; set; }        
    }

    [DataContract]
    public class SkipCustomerRequest
    {
        [DataMember(EmitDefaultValue = false)]
        public long CustID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long ExcludeLeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SkipReason  { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16? SkipDuration { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsMins { get; set; }
        
    }
    [DataContract]
    [Serializable]
    public class RemoveNext5Leads
    {
        [DataMember(EmitDefaultValue = false)]
        public string EmpCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long ParentId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CustomerName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long RemoveBy { get; set; }

    }


    public static class SkipMaster
    {
        #region Skip Reasons
        public static readonly string VIDEOMEET_SKIP = "VIDEOMEET_SKIP";
        #endregion

        // skipreason minutes
        public static readonly Dictionary<string, Int16> ReasonDurationDict = new() {
            {"VIDEOMEET_SKIP", Convert.ToInt16(61) },
        };
    }
    [DataContract]
    public class ReleasePointData
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EmployeeID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public byte Status { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime RequestDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime ExpiryDate { get; set; }

    }

    public enum CallSentimentEnum
    {
        None = 0,
        Negative = 1,
        StrongNegative = 2
    }
    public enum DNCModelEnum
    {
        None = 0,
        Orange = 1,
        Red = 2
    }
    public enum UnsubscribeLevel
    {
        DoNotUnsubscribe = 0,
        UnsubCurrentLead = 1,
        UnsubCustAllLeadsWithLowTT = 2
    }
    public class SentimentCoolingLogic
    {
        public Int16 MinExpDays { get; set; }
        public Int16 MaxExpDays { get; set; }
        public Int16 CoolingPeriod { get; set; }
        public Int16 DNCCount { get; set; }
    }

    [DataContract]
    [Serializable]
    [BsonIgnoreExtraElements]
    public class CustomerNANC
    {

        public Int64 CustID { get; set; }
        public Int16 TodayNANCCount { get; set; }
        private DateTime _callDate;

        [DataMember(EmitDefaultValue = false)]
        public DateTime CallDate
        {
            get
            {
                if (_callDate.Kind == DateTimeKind.Utc)
                {
                    _callDate = _callDate.ToLocalTime();
                }
                return _callDate;
            }
            set { _callDate = value; }
        }

        private DateTime _updatedOn;

        [DataMember(EmitDefaultValue = false)]
        public DateTime UpdatedOn
        {
            get
            {
                if (_updatedOn.Kind == DateTimeKind.Utc)
                {
                    _updatedOn = _updatedOn.ToLocalTime();
                }
                return _updatedOn;
            }
            set { _updatedOn = value; }
        }
        
    }
    public class LogInDTO
    {
        public string EmployeeId { get; set; }
        public string Password { get; set; }
        public string IPAddress { get; set; }
        public int LogInType { get; set; }
        public int LogOutType { get; set; }
        public long LogInBy { get; set; }
        public long LogOutBy { get; set; }
        public bool IsActive { get; set; }
        public long UserId { get; set; }
        public string URL { get; set; }
        public string SessionId { get; set; }
        public int LoginButtonId { get; set; }
        public string Device { get; set; }
        public string Resolution { get; set; }
        public Decimal DSpeed { get; set; }
        public long LeadId { get; set; }
        public int RoleID { get; set; }
    }
    [DataContract]
    [BsonIgnoreExtraElements]
    public class CustEmergencyNo
    {
        private DateTime _ValidTill;

        [DataMember(EmitDefaultValue = false)]
        public int CountryCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EncNumber { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime ValidTill
        {
            get
            {
                if (_ValidTill != null && _ValidTill.Kind == DateTimeKind.Utc)
                {
                    _ValidTill = _ValidTill.ToLocalTime();
                }
                return _ValidTill;
            }
            set { this._ValidTill = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public long CustMobId { get; set; }
    }

    [BsonIgnoreExtraElements]    
    public class UnassistedPilotLead    
    {   
        public string LeadId { get; set; }
        public string CustomerId { get; set; }
    }
}